services:
  # NodeJS service for backend
  server:
    container_name: 'advayta_server'
    working_dir: '/server'
    build:
      context: ./server/.
    env_file:
      - .env
    volumes:
      - ./server/src:/server/src
      - ./server/upload:/server/upload
      - /server/node_modules
    command: sh -c "NODE_OPTIONS='--max-old-space-size=8192' npm run start:dev"
    depends_on:
      postgresql:
        condition: service_healthy
    ports:
      - '9015:9015'
    networks:
      - internal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9015/api/client/translation/ru"]
      interval: 3s
      timeout: 3s
      retries: 5
      start_period: 3s

  # Angular service for frontend with SSR
  client:
    container_name: 'advayta_client_ssr'
    working_dir: '/client'
    env_file:
      - .env
    build:
      context: ./client/.
      dockerfile: Dockerfile.dev-ssr
    volumes:
      - ./client/src:/client/src
      - /client/node_modules
    ports:
      - '9019:9019'
    depends_on:
      server:
        condition: service_healthy
    networks:
      - internal

  admin:
    container_name: 'advayta_admin'
    working_dir: '/admin'
    env_file:
      - .env
    build:
      context: ./admin/.
      target: development
    volumes:
      - ./admin/src:/admin/src
      - /admin/node_modules
    networks:
      - internal
    ports:
      - '9016:9016'

  postgresql:
    container_name: 'advayta_postgres'
    image: postgres:17
    env_file:
      - .env
    healthcheck:
      test: pg_isready -h localhost -U advayta
      interval: 5s
      timeout: 10s
      retries: 10
    volumes:
      - postgresql-data:/var/lib/postgresql/data
    networks:
      - internal
    ports:
      - '0.0.0.0:54321:5432'

networks:
  internal:

volumes:
  postgresql-data:

{"name": "client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --port 9019 --host 0.0.0.0", "start:local": "ng serve --port 9019 --host 0.0.0.0 --configuration development", "build": "ng build --configuration production", "build:dev": "ng build --configuration development", "build:dev-ssr": "ng build --configuration development-ssr", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:client": "node dist/client/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/platform-server": "^19.0.0", "@angular/router": "^19.0.0", "@angular/ssr": "^19.0.0", "@fancyapps/ui": "^5.0.36", "@jsverse/transloco": "^7.5.0", "@ng-select/ng-select": "^14.2.0", "@semantic-components/re-captcha": "^0.42.0", "cookie-parser": "^1.4.6", "dotenv": "^16.4.5", "epubjs": "^0.3.93", "express": "^4.18.2", "js-cookie": "^3.0.5", "moment": "^2.30.1", "ngx-cookie-service": "^19.0.0", "ngx-cookie-service-ssr": "^19.0.0", "nodemon": "^3.1.7", "rxjs": "~7.8.0", "slugify": "^1.6.6", "tslib": "^2.8.0", "wavesurfer.js": "^7.8.6", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/js-cookie": "^3.0.6", "@types/node": "^18.19.59", "autoprefixer": "^10.4.20", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "~5.6.0"}}
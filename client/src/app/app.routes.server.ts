import { RenderMode, ServerRoute } from '@angular/ssr';

export const serverRoutes: ServerRoute[] = [
  {
    path: ':lang',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/photo',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/photo/:id',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', id: '1' },
      { lang: 'en', id: '1' }
    ]
  },
  {
    path: ':lang/audiogallery/audiolektsii/',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/audiogallery/audiolektsii/:param',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', param: 'all' },
      { lang: 'en', param: 'all' }
    ]
  },
  {
    path: ':lang/library',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/library/:code',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', code: 'main' },
      { lang: 'en', code: 'main' }
    ]
  },
  {
    path: ':lang/profile/',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/profile/favorites/:subtab',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', subtab: 'all' },
      { lang: 'en', subtab: 'all' }
    ]
  },
  {
    path: ':lang/profile/playlists',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/sitemap',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/mypage/:id',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', id: '1' },
      { lang: 'en', id: '1' }
    ]
  },
  {
    path: ':lang/forum/:id',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', id: '1' },
      { lang: 'en', id: '1' }
    ]
  },
  {
    path: ':lang/forum/topic/:id',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', id: '1' },
      { lang: 'en', id: '1' }
    ]
  },
  {
    path: ':lang/categories/',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/categories/:id',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', id: '1' },
      { lang: 'en', id: '1' }
    ]
  },
  {
    path: ':lang/categories/:id/:page',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', id: '1', page: '1' },
      { lang: 'en', id: '1', page: '1' }
    ]
  },
  {
    path: ':lang/:category/:page',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru', category: 'news', page: '1' },
      { lang: 'en', category: 'news', page: '1' }
    ]
  },
  {
    path: ':lang/news',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/signin',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  {
    path: ':lang/forgot',
    renderMode: RenderMode.Prerender,
    getPrerenderParams: async () => [
      { lang: 'ru' },
      { lang: 'en' }
    ]
  },
  { path: '**', renderMode: RenderMode.Server }
];

import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class ContentOptimizationService {
  private platformId = inject(PLATFORM_ID);

  /**
   * Optimizes dynamic HTML content for better performance
   */
  optimizeHtmlContent(contentElement: HTMLElement): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Optimize images
    this.optimizeContentImages(contentElement);
    
    // Optimize text content
    this.optimizeTextContent(contentElement);
    
    // Setup lazy loading for content chunks
    this.setupContentLazyLoading(contentElement);
    
    // Optimize links
    this.optimizeLinks(contentElement);
  }

  private optimizeContentImages(contentElement: HTMLElement): void {
    const images = contentElement.querySelectorAll('img');
    
    images.forEach((img, index) => {
      // First 3 images are critical
      if (index < 3) {
        img.setAttribute('fetchpriority', 'high');
        img.loading = 'eager';
        img.style.willChange = 'auto';
      } else {
        img.setAttribute('fetchpriority', 'low');
        img.loading = 'lazy';
        img.style.contentVisibility = 'auto';
        img.style.containIntrinsicSize = 'auto 200px';
      }
      
      // Add responsive sizing
      if (!img.hasAttribute('sizes')) {
        img.setAttribute('sizes', '(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 800px');
      }
      
      // Add error handling
      img.addEventListener('error', () => {
        img.style.display = 'none';
      });
    });
  }

  private optimizeTextContent(contentElement: HTMLElement): void {
    const textElements = contentElement.querySelectorAll('p, div, section, article');
    
    textElements.forEach((element, index) => {
      const htmlElement = element as HTMLElement;
      
      // Only optimize elements below the fold
      if (index > 5) {
        htmlElement.style.contentVisibility = 'auto';
        htmlElement.style.containIntrinsicSize = 'auto 100px';
        htmlElement.style.contain = 'layout style';
      } else {
        // Critical content should be visible immediately
        htmlElement.style.contentVisibility = 'visible';
        htmlElement.style.contain = 'layout style paint';
      }
    });
  }

  private setupContentLazyLoading(contentElement: HTMLElement): void {
    if (!('IntersectionObserver' in window)) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement;
          element.style.contentVisibility = 'visible';
          
          // Trigger any lazy-loaded content
          this.triggerLazyContent(element);
          
          observer.unobserve(element);
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.1
    });

    // Observe content chunks below the fold
    const chunks = contentElement.querySelectorAll('[style*="content-visibility: auto"]');
    chunks.forEach(chunk => observer.observe(chunk));
  }

  private triggerLazyContent(element: HTMLElement): void {
    // Trigger lazy images
    const lazyImages = element.querySelectorAll('img[loading="lazy"]');
    lazyImages.forEach(img => {
      if (img.getAttribute('data-src')) {
        img.setAttribute('src', img.getAttribute('data-src')!);
        img.removeAttribute('data-src');
      }
    });

    // Trigger lazy iframes (for videos, embeds)
    const lazyIframes = element.querySelectorAll('iframe[data-src]');
    lazyIframes.forEach(iframe => {
      iframe.setAttribute('src', iframe.getAttribute('data-src')!);
      iframe.removeAttribute('data-src');
    });
  }

  private optimizeLinks(contentElement: HTMLElement): void {
    const links = contentElement.querySelectorAll('a[href]');
    
    links.forEach(link => {
      const href = link.getAttribute('href');
      
      if (href) {
        // Add prefetch for internal links
        if (this.isInternalLink(href)) {
          link.setAttribute('rel', 'prefetch');
        } else {
          // Add security attributes for external links
          link.setAttribute('rel', 'noopener noreferrer');
          link.setAttribute('target', '_blank');
        }
      }
    });
  }

  private isInternalLink(href: string): boolean {
    return href.startsWith('/') || href.startsWith(window.location.origin);
  }

  /**
   * Preloads critical resources for the content
   */
  preloadCriticalResources(content: any): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Preload main image
    if (content.preview?.name) {
      this.preloadImage(`/upload/${content.preview.name}`);
    }

    // Preload critical fonts if any
    this.preloadCriticalFonts();
  }

  private preloadImage(src: string): void {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    link.setAttribute('fetchpriority', 'high');
    document.head.appendChild(link);
  }

  private preloadCriticalFonts(): void {
    const criticalFonts = [
      '/assets/fonts/Prata-Regular.woff2',
      '/assets/fonts/IBMPlexSans-Regular.woff2'
    ];

    criticalFonts.forEach(fontUrl => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.href = fontUrl;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  }

  /**
   * Optimizes content for Core Web Vitals
   */
  optimizeForWebVitals(contentElement: HTMLElement): void {
    // Reduce layout shifts
    this.preventLayoutShifts(contentElement);
    
    // Optimize for LCP
    this.optimizeForLCP(contentElement);
    
    // Optimize for FID
    this.optimizeForFID(contentElement);
  }

  private preventLayoutShifts(contentElement: HTMLElement): void {
    // Add dimensions to images without them
    const images = contentElement.querySelectorAll('img:not([width]):not([height])');
    images.forEach(img => {
      // img.style.aspectRatio = '16 / 9'; // Default aspect ratio
      // img.style.width = '100%';
      // img.style.height = 'auto';
    });

    // Reserve space for dynamic content
    const dynamicElements = contentElement.querySelectorAll('[data-dynamic]');
    dynamicElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      htmlElement.style.minHeight = '100px'; // Reserve minimum space
    });
  }

  private optimizeForLCP(contentElement: HTMLElement): void {
    // Find potential LCP elements
    const lcpCandidates = contentElement.querySelectorAll('img, h1, p:first-of-type');
    
    lcpCandidates.forEach((element, index) => {
      if (index === 0) {
        const htmlElement = element as HTMLElement;
        htmlElement.style.willChange = 'auto';
        
        if (element.tagName === 'IMG') {
          element.setAttribute('fetchpriority', 'high');
          (element as HTMLImageElement).loading = 'eager';
        }
      }
    });
  }

  private optimizeForFID(contentElement: HTMLElement): void {
    // Defer non-critical JavaScript
    const scripts = contentElement.querySelectorAll('script');
    scripts.forEach(script => {
      if (!script.hasAttribute('async') && !script.hasAttribute('defer')) {
        script.defer = true;
      }
    });

    // Use passive event listeners where possible
    const interactiveElements = contentElement.querySelectorAll('button, a, [onclick]');
    interactiveElements.forEach(element => {
      element.addEventListener('touchstart', () => {}, { passive: true });
    });
  }
}

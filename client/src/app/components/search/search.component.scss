@use "../../pages/audio-gallery/audio-gallery.component.scss";

.art_img img {
  width: 52px;
}

.lik_hov {
  .on_hov {
    left: -47px;
  }
}

.search .form-input {
  background: #f9f9f9;
  width: 100%;
  padding: 10px 20px;
  border-radius: 30px;
  outline: none;
}

.btn-search {
  background: #464646;
  border-radius: 30px;
  color: white;
  padding: 0px 25px;
  margin-left: 20px;
}

.search-wrap {
  max-width: 990px;
  padding: 100px 30px 120px;
  margin: 0 auto 0;
  min-height: calc(100vh - 80px);
  &.empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.search-title {
  font-weight: 600;
  margin-bottom: 10px;
  cursor: pointer;
}

.search-item {
  padding: 20px;
  background: #f9f9f9;
  border-radius: 10px;
  margin: 20px 0px;
  position: relative;
}

.search-label {
  position: absolute;
  right: 15px;
  top: 10px;
  font-size: 14px;
  color: #bdbdbd;
}

.search-term {
  font-weight: bold!important;
}


.term-format {
  background: yellow!important;
}

button.color {
  background-color: #c6c6c6;
  border-radius: 5px;
  margin: 0 10px;
  padding: 0 6px;
}

.p-2 {
  background-color: #edeae6;
  width: fit-content;
  margin: 20px auto;
  border-radius: 10px;
}


.articles-search {
  background: url(assets/images/filter_r_search.svg);
  height: 80px;
  background-position: center !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
}

.articles-search-btn_ {
  position: absolute;
  height: 80px;
  width: 137px;
  right: 0;
  top: 0;
  cursor: pointer;
}

.articles-search input {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  color: var(--font-color1);
  outline: none;
  padding: 5px 140px 5px 85px;
  border: none;
  height: 80px;
  width: 100%;
  background: transparent;
}

.articles-search input::placeholder {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  color: var(--font-color1);
}

.search-tabs {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  max-width: 930px;
  margin: 40px auto 68px;
}

.next-button {
  transform: rotate(180deg);
}

.search-tab {
font-family: Prata;
font-weight: 400;
font-size: 24px;
line-height: 24px;
letter-spacing: 0;
text-align: center;
cursor: pointer;
color: var(--font-color1);
display: flex;
align-items: center;
}

.search-tab.active {
color: var(--text-color);
}

.v-divider {
  width: 1px;
  min-width: 1px;
  height: 38px;
  background: var(--text-color);
}

.mobile-search-tabs {
  display: none;
}

.photo-list {
  display: flex;
  justify-content: left;
  flex-wrap: wrap;
  max-width: 930px;
  margin: 0 auto;
  gap: 30px;
  width: 100%;
}

.photo-item {
  flex-basis: calc(33.33% - 20px);
  max-width: calc(33.33% - 20px);
  border-radius: 20px;
  height: 290px;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  &:hover {
    .photo-item-info {
      transform: translateY(0);
    }
  }
}

.photo-item-info {
  background-color: rgb(83 46 0 / 80%);
  padding: 16px;
  width: 100%;
  height: 290px;
  min-height: fit-content;
  // max-height: 290px;
  position: absolute;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  gap: 22px;
  border-radius: 0 0 20px 20px;
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0;
  color: #fff;
  transform: translateY(101%);
  transition: transform 200ms ease-in-out;
}

.photo-item img {
  max-width: 290px;
  max-height: 290px;
  object-fit: cover;
}

.library-list {
  max-width: 930px;
  margin: 0 auto;
  display: flex;
  gap: 60px;
  width: 100%;
  .book-item {
    flex-basis: calc(33.33% - 40px);
    max-width: calc(33.33% - 40px);
  }
}

.forum-list, .article-list {
  max-width: 930px;
  margin: 0 auto;
  display: flex;
  width: 100%;
}

.article-item {
  padding: 26px 0;
  border-top: 1px solid var(--text-color);
  &:last-of-type {
    border-bottom: 1px solid var(--text-color);
  }
  .article-item-icon {
    margin-right: 40px;
    min-width: 35px;
  }
  .article-item-content-wrapper {
    max-width: 500px;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0;
    color: var(--font-color1);

    .article-item-content {
      max-height: 48px;
      overflow: hidden;
      line-height: 24px;
    }
    .article-item-author {
      color: var(--text-color);
    }
    .article-item-read-more {
      width: fit-content;
      border-bottom: 1px solid var(--font-color1);
    }
  }
}

.forum-item {
  padding: 26px 0;
  border-top: 1px solid var(--text-color);
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 17px;
  letter-spacing: 0;
  color: var(--font-color1);

  &:last-of-type {
    border-bottom: 1px solid var(--text-color);
  }
  .forum-main-content {
    display: flex;
    .forum-name {
      font-size: 20px;
      line-height: 24px;
    }
    .forum-last-answer {
      span {
        color: var(--text-color);
      }
    }
    .forum-item-right-content {
      height: inherit;
      .forum-answers-count {
        color: var(--text-color);
      }
    }
  }
}

.go-to-forum-btn {
  border-radius: 15px;
  border: 1px solid var(--text-color);
  line-height: 24px;
  padding: 8px 20px;
}

.on_hov {
  display: none;
  position: absolute;
  font-family: Prata;
  font-weight: 400;
  font-size: 15px;
  line-height: 20px;
  height: 50px;
  padding: 0 16px;
  background-color: var(--selection);
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  color: var(--font-color);
  white-space: nowrap;
  top: 33px;
  z-index: 5;
}

.on_hov::before {
  content: '';
  background-image: var(--triangl_w);
  display: block;
  background-repeat: no-repeat;
  background-position: center;
  width: 48px;
  height: 17px;
  position: absolute;
  top: -10px;
}

.lik_hov:hover {
  .on_hov {
    display: flex;
  }
}

.shr_hov:hover {
  .on_hov {
    display: flex;
  }
}

.fav_hov:hover {
  .on_hov {
    display: flex;
  }
}

.lik_hov {
  position: relative;

  .on_hov {
    left: -85px;
  }

  .on_hov::before {
    right: 14px;
  }
}

.shr_hov {
  position: relative;

  .on_hov {
    left: -47px;
  }
}

.fav_hov {
  position: relative;

  .on_hov {
    left: -30px;
  }

  .on_hov::before {
    left: 19px;
  }
}

.desktop-actions {
  display: flex;
}

.mobile-actions {
  display: none;
}



@media (max-width: 1250px) {
  .search-tab {
    font-size: 20px;
  }
  .search-tabs, .photo-list, .library-list, .forum-list, .article-list {
    max-width: 768px;
  }

  .photo-list {
    gap: 20px;
  }

  .library-list {
    column-gap: 40px;
    row-gap: 60px;
    .book-item {
      flex-basis: calc(33.33% - 28px);
      max-width: calc(33.33% - 28px);
    }
  }

  .article-item {
    padding: 21px 0 30px;
    .article-item-content-wrapper {
      font-size: 18px;
      line-height: 20px;
  
      .article-item-content {
        max-height: 44px;
        line-height: 22px;
      }
    }
  }
  
  .forum-item {
    padding: 21px 0 30px;
    font-size: 15px;
    line-height: 15px;
    .forum-main-content {
      .forum-name {
        font-size: 18px;
        line-height: 20px
      }
    }
  }
}
@media (max-width: 768px) {
  .articles-search input {
    font-size: 18px;
    line-height: 18px;
    padding: 5px 15% 5px 51px;
  }

  .articles-search input::placeholder {
    font-size: 18px;
    line-height: 18px;
  }

  .articles-search input::placeholder {
    font-size: 18px;
    line-height: 18px;
  }
  .search-tabs {
    display: none;
  }

  .mobile-search-tabs {
    display: flex;
    margin: 40px auto 40px;
    padding: 0 10px;
    max-width: 580px;
  }

  .desktop-actions {
    display: none;
  }

  .mobile-actions {
    display: block;
    cursor: pointer;
    width: 20px;
    display: flex;
    justify-content: flex-end;
    height: 25px;
    position: relative;
    margin-top: 18px;
  }

  .photo-item {
    border-radius: 15px;
    height: 180px;
  }

  .photo-item img {
    max-height: 180px;
  } 

  .photo-item-actions {
    zoom: 0.8;
  }

  .photo-item-info{
    font-size: 12px;
    line-height: 14px;
    height: 180px;
    border-radius: 0 0 15px 15px;
  }

  .forum-item {
    .forum-main-content {
      flex-direction: column;
      gap: 15px;
      .actions_w {
        .icons_w {
          display: none;
        }
      }
      .go-to-forum-btn {
        padding: 8px 25px;
        font-size: 15px;
        line-height: 15px;
        width: 120px;
        border-radius: 10px;
      }
      .forum-item-right-content {
        flex-direction: column-reverse;
        .forum-answers-count {
          justify-content: flex-start;
        }
      }
    }
  }
}

@media (max-width: 650px) {
  .articles-search input {
    padding: 5px 20% 5px 20%;
  }
  .photo-item {
    flex-basis: calc(50% - 10px);
    max-width: calc(50% - 10px);
  }
  .library-list {
    column-gap: 30px;
    row-gap: 40px;
    max-width: 460px;
    padding: 0 8px;
    .book-item {
      flex-basis: calc(50% - 15px);
      max-width: calc(50% - 15px);
    }
  }
  .article-item {
    padding: 16px 0 26;
    .article-item-content-wrapper {
      gap: 6px;
      font-size: 15px;
      line-height: 17px;
      max-width: 100%;
  
      .article-item-content {
        max-height: 36px;
        line-height: 18px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
      }
    }
    .mobile-actions {
      margin-top: 10px;
    }
  }

  .forum-item {
    padding: 16px 0 26px;
    font-size: 15px;
    line-height: 19px;
    .forum-main-content {
      gap: 10px;
      .forum-name {
        font-size: 15px;
        line-height: 19px;
      }
      .go-to-forum-btn {
        padding: 6px 20px;
        font-size: 13px;
        line-height: 13px;
        width: 100px;
      }
    }
  }
}

@media (max-width: 570px) {
  .articles-search {
    background: url(assets/images/filters_sm_search.svg);
    background-size: contain !important;
  }
  .articles-search input {
    padding: 5px 13% 5px 13%;
  }
  .articles-search input {
    font-size: 14px;
    line-height: 14px;
  }

  .articles-search input::placeholder {
    font-size: 14px;
    line-height: 14px;
  }
}

@media (max-width: 450px) {
  .photo-item {
    height: 160px;
  }

  .photo-item img {
    max-height: 160px;
  } 

  .photo-item-info{
    height: 160px;
  }
}
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Resource hints for performance -->
  <link rel="dns-prefetch" href="//dev.advayta.org">
  <link rel="preconnect" href="//dev.advayta.org" crossorigin>

  <!-- Preload critical resources with dynamic names -->
  <link rel="preload" as="script" href="polyfills.js" fetchpriority="high">
  <link rel="preload" as="script" href="main.js" fetchpriority="high">
  <link rel="preload" as="style" href="styles.css" fetchpriority="high">

  <!-- Additional preconnects for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  
</head>
<body>
  <style>
    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      transition: opacity 0.3s ease;
      opacity: 1;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(222, 165, 61, 0.3);
      border-top: 3px solid #dea53d;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      -webkit-animation: spin 1s linear infinite;
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      will-change: transform;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @-webkit-keyframes spin {
      0% { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }
    
    .preloader-hidden {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
    }
  </style>
  <div class="preloader" id="preloader">
    <div class="spinner"></div>
  </div>
  <app-root></app-root>
</body>
</html>

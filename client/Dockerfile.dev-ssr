FROM node:22-alpine as base
WORKDIR /client
COPY package*.json ./
RUN apk add make gcc g++ python3
RUN npm ci --force
COPY . .

# Development with SSR - build once and serve with file watching
RUN npm run build:dev-ssr
EXPOSE 9019

# Use nodemon to watch for changes and rebuild
CMD ["npx", "nodemon", "--watch", "src", "--ext", "ts,html,scss,css", "--exec", "npm run build:dev-ssr && npm run serve:ssr:client"]
